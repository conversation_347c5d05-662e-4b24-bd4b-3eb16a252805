# 🤖 Delphi - The Logistics AI

Sistema de análise de dados logísticos usando IA com ferramentas de contexto (padrão MCP) para fornecer insights inteligentes sobre operações logísticas.

## ✨ Features

The Delphi AI offers the following tools:

- **📊 Unscheduled Routes** - Finds active routes without a defined schedule
- **💰 Client Financial Summary** - Calculates financial summaries by period
- **📋 Contract Alerts** - Identifies contracts nearing expiration
- **🚨 Overdue Clients** - Lists clients with the largest overdue amounts

## 🚀 Installation and Configuration

### Prerequisites

- Python 3.12+
- MySQL/MariaDB
- OpenAI API Key

### Configuration

1. **Clone the repository**
```bash
git clone <repository-url>
cd delphi
```

2. **Configure environment variables**
```bash
cp .env.example .env
# Edit the .env file with your configurations
```

3. **Run with Docker**
```bash
# Build and run with Docker Compose
docker-compose up --build
```

## 📡 API Endpoints

### Analysis Service

- **POST** `/api/v1/analyze` - Submits a query for analysis
- **GET** `/api/v1/analyze/status/{task_id}` - Checks analysis status
- **GET** `/api/v1/analyze/tools` - Lists available tools

### Quick Query

- **GET** `/api/v1/analyzer/analyze?query=your_question` - Direct data analysis

### System Health

- **GET** `/api/v1/health` - Basic health check
- **GET** `/api/v1/health/detailed` - Detailed health check
- **GET** `/api/v1/health/ai` - Specific AI status

## 🛠️ Available Tools

### 1. Rotas Não Agendadas
```json
{
  "tool": "find_unscheduled_routes",
  "parameters": {
    "limit": 50
  }
}
```

### 2. Resumo Financeiro de Cliente
```json
{
  "tool": "get_client_billing_summary",
  "parameters": {
    "client_id": 123,
    "start_date": "2024-01-01",
    "end_date": "2024-12-31"
  }
}
```

### 3. Alertas de Contratos
```json
{
  "tool": "get_contract_renewal_alerts",
  "parameters": {
    "days_ahead": 90
  }
}
```

### 4. Clientes em Atraso
```json
{
  "tool": "get_top_overdue_clients",
  "parameters": {
    "limit": 5
  }
}
```

## 🏗️ Project Structure

```
delphi/
├── config/          # Application configurations
├── database/        # Database connection
├── models/          # Pydantic models (requests/responses)
├── routes/          # API Endpoints
├── services/        # Business Logic (AI)
├── tools/           # AI Tools
│   ├── base.py      # Base class for tools
│   ├── unscheduled_routes/
│   ├── client_billing/
│   ├── contract_alerts/
│   └── overdue_clients/
├── main.py          # Main application
├── requirements.txt # Python dependencies
└── docker-compose.yml
```

## 🔧 Development

### Adding New Tools

1. Create a directory in `tools/new_tool/`
2. Implement `tool.py` inheriting from `BaseTool`
3. The tool will be automatically discovered and registered

Example:
```python
from tools.base import BaseTool, ToolSchema

class MyTool(BaseTool):
    def __init__(self):
        self.schema = ToolSchema(
            name="my_tool",
            description="Tool description",
            parameters={...}
        )
    
    async def execute(self, **kwargs):
        # Tool implementation
        return {"result": "data"}
```

## 📊 Monitoring

The system includes health endpoints for monitoring:

- Database connection
- AI tool status
- OpenAI API connection
- System resources (CPU, memory, disk)

## 🔒 Security

- Non-root user in Docker container
- Input validation with Pydantic
- Robust error handling
- Structured logs

## 📝 License

[Add license information]

## 🤝 Contribution

[Add contribution guidelines]

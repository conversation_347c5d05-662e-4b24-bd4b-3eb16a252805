from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings with environment variable support"""

    # LLM Configuration
    llm: str = "openai"

    # OpenAI Configuration
    openai_api_key: str | None = None
    openai_model: str = "gpt-4o-mini"

    # Google Gemini Configuration
    gemini_api_key: str | None = None
    gemini_model: str = "gemini-2.5-flash"

    # Database Configuration
    mysql_host: str
    mysql_user: str
    mysql_password: str
    mysql_database: str
    mysql_port: int = 3306

    # Application Configuration
    debug: bool = False
    cors_origins: str = "*"

    # Task Configuration
    max_concurrent_tasks: int = 10
    task_timeout_seconds: int = 300

    # Redis Configuration
    redis_host: str = "localhost"
    redis_port: int = 6379
    redis_db: int = 0
    redis_password: str | None = None

    class Config:
        env_file = ".env"
        case_sensitive = False


def get_settings() -> Settings:
    """Get application settings (cached)"""
    return Settings()

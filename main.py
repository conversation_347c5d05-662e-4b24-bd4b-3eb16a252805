from contextlib import asynccontextmanager
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from config.settings import get_settings
from routes.analysis import router as analysis_router
from routes.health import router as health_router
from routes.oracle import router as oracle_router
from services.oracle import DelphiOracle

# Get configuration
settings = get_settings()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan - startup and shutdown events"""

    # Startup
    print("🏛️  Initializing the Oracle of Delphi...")

    ai_settings = {
        "llm": settings.llm,
        "openai_api_key": settings.openai_api_key,
        "openai_model": settings.openai_model,
        "gemini_api_key": settings.gemini_api_key,
        "gemini_model": settings.gemini_model
    }

    # Initialize the Oracle
    app.state.oracle = DelphiOracle(
        ai_settings
    )

    print(f"✨ Delphi Oracle is ready to give insights!")
    print(f"🔮 Available tools: {len(app.state.oracle.tools)}")

    yield

    # Shutdown
    print("🌅 The Oracle of Delphi is closing the temple...")


# Create FastAPI app
app = FastAPI(
    title="Delphi - The Logistics Oracle",
    description="""
    🏛️ **The Oracle of Delphi for Logistics Intelligence**

    Consult the Oracle to divine insights from your logistics data.
    The Oracle uses AI and sacred tools to analyze routes, clients, contracts, and financial data.

    ## Divine Powers

    * **📊 Data Analysis** - Query your logistics data with natural language
    * **🔮 Predictive Insights** - Get actionable recommendations
    * **⚡ Real-time Processing** - Async task processing for complex queries
    * **🛠️ Sacred Tools** - Automated tool selection and execution

    Ask the Oracle anything about your logistics operations!
    """,
    version="1.0.0",
    lifespan=lifespan,
    docs_url="/",  # Swagger UI at root
    redoc_url="/docs"  # ReDoc at /docs
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(analysis_router, prefix="/api/v1")
app.include_router(health_router, prefix="/api/v1")
app.include_router(oracle_router, prefix="/api/v1")

if __name__ == "__main__":
    # Development server
    print("🏛️ Starting Delphi in development mode...")
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )

from contextlib import asynccontextmanager
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from config.settings import get_settings
from routes.analysis import router as analysis_router
from routes.health import router as health_router
from routes.oracle import router as oracle_router
from services.oracle import DelphiAnalyzer

# Get configuration
settings = get_settings()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan - startup and shutdown events"""

    # Startup
    print("📊 Initializing the Delphi Data Analyzer...")

    ai_settings = {
        "llm": settings.llm,
        "openai_api_key": settings.openai_api_key,
        "openai_model": settings.openai_model,
        "gemini_api_key": settings.gemini_api_key,
        "gemini_model": settings.gemini_model
    }

    # Initialize the Analyzer
    app.state.analyzer = DelphiAnalyzer(
        ai_settings
    )

    print(f"✅ Delphi Analyzer is ready to process data!")
    print(f"🛠️ Available tools: {len(app.state.analyzer.tools)}")

    yield

    # Shutdown
    print("🔄 The Delphi Analyzer is shutting down...")


# Create FastAPI app
app = FastAPI(
    title="Delphi - Logistics Data Analyzer",
    description="""
    📊 **Delphi Logistics Data Analyzer**

    Submit requests to analyze your logistics data and get actionable insights.
    The analyzer uses AI and specialized tools to process routes, clients, contracts, and financial data.

    ## Core Capabilities

    * **📊 Data Analysis** - Query your logistics data with natural language
    * **💡 Actionable Insights** - Get professional recommendations
    * **⚡ Real-time Processing** - Async task processing for complex queries
    * **🛠️ Analysis Tools** - Automated tool selection and execution

    Submit any query about your logistics operations for analysis!
    """,
    version="1.0.0",
    lifespan=lifespan,
    docs_url="/",  # Swagger UI at root
    redoc_url="/docs"  # ReDoc at /docs
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(analysis_router, prefix="/api/v1")
app.include_router(health_router, prefix="/api/v1")
app.include_router(oracle_router, prefix="/api/v1")

if __name__ == "__main__":
    # Development server
    print("📊 Starting Delphi in development mode...")
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )

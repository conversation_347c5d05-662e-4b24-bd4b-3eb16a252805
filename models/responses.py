from pydantic import BaseModel, Field
from typing import Dict, Any, Optional, List
from datetime import datetime
from enum import Enum


class TaskStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETE = "complete"
    FAILED = "failed"


class TaskCreationResponse(BaseModel):
    """Response model for task creation"""

    task_id: str = Field(
        ...,
        description="Unique identifier for the created task",
    )

    status_url: str = Field(
        ...,
        description="URL to check the task status",
    )

    oracle_message: str = Field(
        ...,
        description="System acknowledgment message",
    )

    estimated_completion: Optional[str] = Field(
        default=None,
        description="Estimated completion time",
    )


class OracleResult(BaseModel):
    """Analysis result data"""

    analysis_result: str = Field(
        ...,
        description="The analyzer's insights and findings"
    )

    processed_data: Dict[str, Any] = Field(
        ...,
        description="Data processed by the analysis tools"
    )

    tools_invoked: List[str] = Field(
        ...,
        description="List of tools used in the analysis"
    )

    analysis_status: str = Field(
        ...,
        description="Status of the analysis process"
    )

    confidence_level: Optional[str] = Field(
        default=None,
        description="Confidence level in the analysis results"
    )


class TaskStatusResponse(BaseModel):
    """Response model for task status"""

    task_id: str = Field(
        ...,
        description="Unique identifier for the task"
    )

    status: TaskStatus = Field(
        ...,
        description="Current status of the task"
    )

    query: str = Field(
        ...,
        description="Original query submitted for analysis"
    )

    result: Optional[OracleResult] = Field(
        default=None,
        description="Analysis result (available when complete)"
    )

    created_at: str = Field(
        ...,
        description="Task creation timestamp"
    )

    completed_at: Optional[str] = Field(
        default=None,
        description="Task completion timestamp"
    )

    oracle_message: str = Field(
        ...,
        description="Current system status message"
    )

    progress_percentage: Optional[int] = Field(
        default=None,
        description="Progress percentage (0-100)"
    )


class DivineTool(BaseModel):
    """Model for analysis tools"""

    name: str = Field(
        ...,
        description="Tool name",
    )

    description: str = Field(
        ...,
        description="Tool description",
    )

    tool_status: str = Field(
        ...,
        description="Status of the analysis tool",
    )

    parameters: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Tool parameters schema"
    )


class ToolsListResponse(BaseModel):
    """Response model for tools listing"""

    tools: List[DivineTool] = Field(
        ...,
        description="List of available analysis tools"
    )

    total_count: int = Field(
        ...,
        description="Total number of available tools"
    )

    oracle_message: str = Field(
        ...,
        description="System message about the tools",
    )


class HealthStatus(BaseModel):
    """Health check response model"""

    status: str = Field(
        ...,
        description="Overall health status",
    )

    system_status: str = Field(
        ...,
        description="System status message",
    )

    timestamp: str = Field(
        ...,
        description="Health check timestamp"
    )

    service: str = Field(
        ...,
        description="Service name",
    )


class ErrorResponse(BaseModel):
    """Error response model"""

    error: str = Field(
        ...,
        description="Error message"
    )

    error_code: Optional[str] = Field(
        default=None,
        description="Error code for programmatic handling"
    )

    system_guidance: Optional[str] = Field(
        default=None,
        description="System guidance for resolving the error"
    )

    timestamp: str = Field(
        default_factory=lambda: datetime.utcnow().isoformat(),
        description="Error timestamp"
    )

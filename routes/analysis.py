import uuid
import json
from datetime import datetime
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends, Request
from models.requests import AnalysisRequest
from models.responses import TaskCreationResponse, TaskStatus, TaskStatusResponse, ToolsListResponse, OracleResult
from services.redis import get_redis_connection
from services.oracle import DelphiAnalyzer
import logging
from fastapi.encoders import jsonable_encoder
import redis.asyncio as redis

router = APIRouter(prefix="/analysis", tags=["Analysis"])

logger = logging.getLogger(__name__)


def get_analyzer(request: Request) -> DelphiAnalyzer:
    """Dependency to get Analyzer instance"""
    return request.app.state.analyzer


@router.post("/", response_model=TaskCreationResponse, status_code=202)
async def submit_analysis(
        request: AnalysisRequest,
        background_tasks: BackgroundTasks,
        analyzer: DelphiAnalyzer = Depends(get_analyzer),
        redis_conn: redis.Redis = Depends(get_redis_connection)
):
    """
    📊 Submit a query for data analysis

    The analyzer will:
    1. Process your query
    2. Select appropriate analysis tools
    3. <PERSON><PERSON> processed data
    4. Provide actionable insights
    """
    task_id = str(uuid.uuid4())

    # Initialize task in Redis
    task_data = {
        "status": "pending",
        "query": request.query,
        "context": request.context,
        "created_at": datetime.utcnow().isoformat(),
        "result": None
    }
    await redis_conn.set(f"task:{task_id}", json.dumps(task_data))

    # Start background analysis
    background_tasks.add_task(
        analyze_in_background,
        task_id=task_id,
        request=request,
        analyzer=analyzer,
        redis_conn=redis_conn
    )

    return TaskCreationResponse(
        task_id=task_id,
        status_url=f"/api/v1/analysis/{task_id}",
        oracle_message="📊 Analysis request submitted successfully"
    )


@router.get("/{task_id}", response_model=TaskStatusResponse)
async def get_analysis_status(task_id: str, redis_conn: redis.Redis = Depends(get_redis_connection)):
    logger.info(f"Task id: {jsonable_encoder(task_id)}")

    """
    📋 Check the status of your analysis request
    """
    task_data = await redis_conn.get(f"task:{task_id}")
    if not task_data:
        raise HTTPException(status_code=404, detail="Analysis request not found in the system records")

    try:
        task = json.loads(task_data)

        # Log para debug
        logger.info(f"Task data: {jsonable_encoder(task)}")

        # Verificar se todos os campos necessários existem
        required_fields = ["status", "query", "created_at"]
        for field in required_fields:
            if field not in task:
                logger.error(f"Missing required field '{field}' in task {task_id}")
                task[field] = "unknown" if field != "created_at" else datetime.utcnow().isoformat()

        # Garantir que result seja serializável
        result_data = task.get("result")
        oracle_result = None
        if isinstance(result_data, dict):
            try:
                oracle_result = OracleResult.model_validate(result_data)
            except Exception as e:
                logger.warning(f"Failed to parse OracleResult in task {task_id}: {e}")
                task["result"] = str(result_data)  # Fallback to string representation

        return TaskStatusResponse(
            task_id=task_id,
            status=task["status"],
            query=task["query"],
            result=oracle_result,
            created_at=task["created_at"],
            oracle_message=get_status_message(task["status"])
        )
    except Exception as e:
        logger.exception(f"Error processing task status for {task_id}: {str(e)}")

        return TaskStatusResponse(
            task_id=task_id,
            status=TaskStatus.DIVINATION_FAILED,
            query="Error retrieving query",
            result=OracleResult.model_validate({"error": f"Failed to process task data: {str(e)}"}),
            created_at=datetime.utcnow().isoformat(),
            oracle_message="⚠️ The analyzer encountered issues while retrieving your results."
        )


async def analyze_in_background(task_id: str, request: AnalysisRequest, analyzer: DelphiAnalyzer, redis_conn: redis.Redis):
    """Background task for data analysis"""
    try:
        # Update status to processing
        task_data = await redis_conn.get(f"task:{task_id}")
        if task_data:
            task = json.loads(task_data)
            task["status"] = "processing"
            await redis_conn.set(f"task:{task_id}", json.dumps(task))

        # The analyzer processes the data
        result = await analyzer.analyze_data(request.query, request.context)

        # Update status to complete and store result
        task_data = await redis_conn.get(f"task:{task_id}")
        if task_data:
            task = json.loads(task_data)
            task["status"] = "complete"
            task["result"] = result
            task["completed_at"] = datetime.utcnow().isoformat()
            await redis_conn.set(f"task:{task_id}", json.dumps(task))

    except Exception as e:
        # Update status to failed and store error
        task_data = await redis_conn.get(f"task:{task_id}")
        if task_data:
            task = json.loads(task_data)
            task["status"] = "failed"
            task["result"] = {
                "error": f"Analysis processing failed: {str(e)}",
                "guidance": "Please check the input or try again later."
            }
            await redis_conn.set(f"task:{task_id}", json.dumps(task))


def get_status_message(status: str) -> str:
    """Get analysis status message"""
    messages = {
        "pending": "📊 Your request is queued for processing...",
        "processing": "🔄 The analyzer is processing your data...",
        "complete": "✅ Analysis complete! Your insights are ready.",
        "failed": "⚠️ The analyzer encountered processing issues."
    }
    return messages.get(status, "🤔 Analysis status is unknown...")


@router.get("/quick", tags=["Analysis"])
async def quick_analysis(query: str, analyzer: DelphiAnalyzer = Depends(get_analyzer)):
    """
    ⚡ Quick data analysis

    For simple requests that don't require background processing.
    Returns immediate results without task tracking.
    """
    if not query:
        return {
            "success": False,
            "error": "Query parameter is required",
            "message": "Please provide a query parameter for analysis"
        }

    try:
        # Simple, direct analysis
        result = await analyzer.analyze_data(query)
        return {
            "success": True,
            "data": {
                "query": query,
                "analysis_result": result,
                "analysis_type": "quick"
            },
            "message": "Quick analysis completed successfully"
        }
    except Exception as e:
        return {
            "success": False,
            "error": f"Analysis failed: {str(e)}",
            "message": "Try a more specific query or use the full analysis endpoint"
        }


@router.get("/tools", response_model=ToolsListResponse, tags=["Tools"])
async def list_analysis_tools(analyzer: DelphiAnalyzer = Depends(get_analyzer)):
    """
    🛠️ List all available analysis tools
    """
    tools = analyzer.list_analysis_tools()
    return ToolsListResponse(
        tools=tools,
        total_count=len(tools),
        oracle_message="🛠️ These are the analysis tools available in the system"
    )

import uuid
import json
from datetime import datetime
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends, Request
from models.requests import AnalysisRequest
from models.responses import TaskCreationResponse, TaskStatus, TaskStatusResponse, ToolsListResponse, OracleResult
from services.redis import get_redis_connection
from services.oracle import DelphiAnalyzer
import logging
from fastapi.encoders import jsonable_encoder
import redis.asyncio as redis

router = APIRouter(prefix="/analyze", tags=["Analysis"])

logger = logging.getLogger(__name__)


def get_oracle(request: Request) -> DelphiOracle:
    """Dependency to get Oracle instance"""
    return request.app.state.oracle


@router.post("/", response_model=TaskCreationResponse, status_code=202)
async def divine_analysis(
        request: AnalysisRequest,
        background_tasks: BackgroundTasks,
        oracle: DelphiOracle = Depends(get_oracle),
        redis_conn: redis.Redis = Depends(get_redis_connection)
):
    """
    🔮 Submit a query to the Oracle for divine analysis

    The Oracle will:
    1. Analyze your query
    2. Select appropriate divine tools
    3. Gather sacred data
    4. Provide prophetic insights
    """
    task_id = str(uuid.uuid4())

    # Initialize task in Redis
    task_data = {
        "status": "pending",
        "query": request.query,
        "context": request.context,
        "created_at": datetime.utcnow().isoformat(),
        "result": None
    }
    await redis_conn.set(f"task:{task_id}", json.dumps(task_data))

    # Start background divination
    background_tasks.add_task(
        divine_in_background,
        task_id=task_id,
        request=request,
        oracle=oracle,
        redis_conn=redis_conn
    )

    return TaskCreationResponse(
        task_id=task_id,
        status_url=f"/api/v1/analyze/status/{task_id}",
        oracle_message="🏛️ The Oracle is consulting the sacred data..."
    )


@router.get("/status/{task_id}", response_model=TaskStatusResponse)
async def get_divination_status(task_id: str, redis_conn: redis.Redis = Depends(get_redis_connection)):
    logger.info(f"Task id: {jsonable_encoder(task_id)}")

    """
    📋 Check the status of your Oracle consultation
    """
    task_data = await redis_conn.get(f"task:{task_id}")
    if not task_data:
        raise HTTPException(status_code=404, detail="Consultation not found in the sacred records")

    try:
        task = json.loads(task_data)

        # Log para debug
        logger.info(f"Task data: {jsonable_encoder(task)}")

        # Verificar se todos os campos necessários existem
        required_fields = ["status", "query", "created_at"]
        for field in required_fields:
            if field not in task:
                logger.error(f"Missing required field '{field}' in task {task_id}")
                task[field] = "unknown" if field != "created_at" else datetime.utcnow().isoformat()

        # Garantir que result seja serializável
        result_data = task.get("result")
        oracle_result = None
        if isinstance(result_data, dict):
            try:
                oracle_result = OracleResult.model_validate(result_data)
            except Exception as e:
                logger.warning(f"Failed to parse OracleResult in task {task_id}: {e}")
                task["result"] = str(result_data)  # Fallback to string representation

        return TaskStatusResponse(
            task_id=task_id,
            status=task["status"],
            query=task["query"],
            result=oracle_result,
            created_at=task["created_at"],
            oracle_message=get_status_message(task["status"])
        )
    except Exception as e:
        logger.exception(f"Error processing task status for {task_id}: {str(e)}")

        return TaskStatusResponse(
            task_id=task_id,
            status=TaskStatus.DIVINATION_FAILED,
            query="Error retrieving query",
            result=OracleResult.model_validate({"error": f"Failed to process task data: {str(e)}"}),
            created_at=datetime.utcnow().isoformat(),
            oracle_message="⚠️ The Oracle encountered turbulence while retrieving your prophecy."
        )


async def divine_in_background(task_id: str, request: AnalysisRequest, oracle: DelphiOracle, redis_conn: redis.Redis):
    """Background task for Oracle divination"""
    try:
        # Update status to consulting_oracle
        task_data = await redis_conn.get(f"task:{task_id}")
        if task_data:
            task = json.loads(task_data)
            task["status"] = "consulting_oracle"
            await redis_conn.set(f"task:{task_id}", json.dumps(task))

        # The Oracle works its magic
        result = await oracle.divine_insights(request.query, request.context)

        # Update status to divination_complete and store result
        task_data = await redis_conn.get(f"task:{task_id}")
        if task_data:
            task = json.loads(task_data)
            task["status"] = "divination_complete"
            task["result"] = result
            task["completed_at"] = datetime.utcnow().isoformat()
            await redis_conn.set(f"task:{task_id}", json.dumps(task))

    except Exception as e:
        # Update status to divination_failed and store error
        task_data = await redis_conn.get(f"task:{task_id}")
        if task_data:
            task = json.loads(task_data)
            task["status"] = "failed"
            task["result"] = {
                "error": f"AI processing failed: {str(e)}",
                "guidance": "Please check the input or try again later."
            }
            await redis_conn.set(f"task:{task_id}", json.dumps(task))


def get_status_message(status: str) -> str:
    """Get Oracle status message"""
    messages = {
        "pending": "🏛️ Your request awaits the Oracle's attention...",
        "consulting_oracle": "🔮 The Oracle is consulting the sacred data...",
        "divination_complete": "✨ The Oracle has spoken! Your insights await.",
        "divination_failed": "⚠️ The Oracle encountered turbulence in the divine realm."
    }
    return messages.get(status, "🤔 The Oracle's status is Unknown...")


@router.get("/tools", response_model=ToolsListResponse, tags=["Tools"])
async def list_divine_tools(oracle: DelphiOracle = Depends(get_oracle)):
    """
    🛠️ List all available divine tools
    """
    tools = oracle.list_divine_tools()
    return ToolsListResponse(
        tools=tools,
        total_count=len(tools),
        oracle_message="🏛️ These are the sacred tools at the Oracle's disposal"
    )

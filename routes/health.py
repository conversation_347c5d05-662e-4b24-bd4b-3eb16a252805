import sys
from datetime import datetime
from fastapi import APIRouter, Depends, Request
from database.connection import test_db_connection
from services.oracle import DelphiOracle

router = APIRouter(prefix="/health", tags=["Health"])


def get_oracle(request: Request) -> DelphiOracle:
    """Dependency to get Oracle instance"""
    return request.app.state.oracle


@router.get("/")
async def health_check():
    """
    🏥 Basic health check for the Oracle temple
    """
    return {
        "status": "healthy",
        "temple_status": "🏛️ Oracle temple is operational",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "Delphi Oracle"
    }


@router.get("/detailed")
async def detailed_health_check(oracle: DelphiOracle = Depends(get_oracle)):
    """
    🔍 Detailed health check including all Oracle systems
    """
    health_data = {
        "timestamp": datetime.utcnow().isoformat(),
        "service": "Delphi Oracle",
        "version": "0.1.0",
        "python_version": sys.version,
        "status": "healthy",
        "checks": {}
    }

    # Check database connection
    try:
        db_status = await test_db_connection()
        health_data["checks"]["database"] = {
            "status": "healthy" if db_status else "unhealthy",
            "message": "📊 Sacred database connection active" if db_status else "❌ Sacred database unavailable"
        }
    except Exception as e:
        health_data["checks"]["database"] = {
            "status": "error",
            "message": f"🚨 Database connection failed: {str(e)}"
        }

    # Check Oracle tools
    try:
        tools_count = len(oracle.tools)
        health_data["checks"]["oracle_tools"] = {
            "status": "healthy" if tools_count > 0 else "warning",
            "message": f"🛠️ {tools_count} divine tools available",
            "tools_loaded": tools_count
        }
    except Exception as e:
        health_data["checks"]["oracle_tools"] = {
            "status": "error",
            "message": f"🚨 Oracle tools unavailable: {str(e)}"
        }

    # Check AI service
    try:
        ai_status = oracle.client is not None
        health_data["checks"]["ai_service"] = {
            "status": "healthy" if ai_status else "unhealthy",
            "message": "🤖 AI Oracle connection active" if ai_status else "❌ AI Oracle disconnected",
            "model": oracle.model
        }
    except Exception as e:
        health_data["checks"]["ai_service"] = {
            "status": "error",
            "message": f"🚨 AI service error: {str(e)}"
        }

    # Check system resources
    try:
        import psutil
        health_data["checks"]["system"] = {
            "status": "healthy",
            "message": "💻 System resources normal",
            "cpu_percent": psutil.cpu_percent(),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_percent": psutil.disk_usage('/').percent
        }
    except ImportError:
        health_data["checks"]["system"] = {
            "status": "info",
            "message": "📊 System monitoring not available (psutil not installed)"
        }
    except Exception as e:
        health_data["checks"]["system"] = {
            "status": "warning",
            "message": f"⚠️ System check failed: {str(e)}"
        }

    # Determine overall status
    failed_checks = [check for check in health_data["checks"].values() if check["status"] in ["error", "unhealthy"]]
    if failed_checks:
        health_data["status"] = "unhealthy"
        health_data["temple_status"] = "🚨 Oracle temple has issues"
    else:
        health_data["temple_status"] = "🏛️ Oracle temple is fully operational"

    return health_data


@router.get("/oracle")
async def oracle_status(oracle: DelphiOracle = Depends(get_oracle)):
    """
    🔮 Check Oracle-specific status and capabilities
    """
    try:
        tools_list = oracle.list_divine_tools()

        return {
            "oracle_status": "ready_for_divination",
            "message": "🔮 The Oracle is ready to divine insights",
            "divine_tools": {
                "total_count": len(tools_list),
                "available_tools": [tool["name"] for tool in tools_list],
                "sacred_powers": tools_list
            },
            "ai_model": oracle.model,
            "temple_condition": "🏛️ Sacred temple is blessed and operational"
        }
    except Exception as e:
        return {
            "oracle_status": "divination_impaired",
            "message": f"⚠️ Oracle experiencing divine interference: {str(e)}",
            "temple_condition": "🌩️ Temple experiencing spiritual turbulence"
        }


@router.get("/readiness")
async def readiness_check(oracle: DelphiOracle = Depends(get_oracle)):
    """
    ✅ Kubernetes readiness probe
    """
    try:
        # Quick checks for essential services
        db_ready = await test_db_connection()
        oracle_ready = len(oracle.tools) > 0
        ai_ready = oracle.client is not None

        if db_ready and oracle_ready and ai_ready:
            return {
                "ready": True,
                "message": "🏛️ Oracle temple ready to receive pilgrims"
            }
        else:
            return {
                "ready": False,
                "message": "⏳ Oracle temple still preparing divine services",
                "details": {
                    "database": db_ready,
                    "oracle_tools": oracle_ready,
                    "ai_service": ai_ready
                }
            }
    except Exception as e:
        return {
            "ready": False,
            "message": f"🚨 Oracle temple not ready: {str(e)}"
        }


@router.get("/liveness")
async def liveness_check():
    """
    💓 Kubernetes liveness probe
    """
    try:
        # Very basic check - just ensure the service is responding
        return {
            "alive": True,
            "message": "💓 Oracle temple heartbeat strong",
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        return {
            "alive": False,
            "message": f"💀 Oracle temple heartbeat weak: {str(e)}"
        }
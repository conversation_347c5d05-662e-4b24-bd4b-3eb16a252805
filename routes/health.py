import sys
from datetime import datetime
from fastapi import APIRouter, Depends, Request
from database.connection import test_db_connection
from services.oracle import DelphiAnalyzer

router = APIRouter(prefix="/health", tags=["Health"])


def get_analyzer(request: Request) -> DelphiAnalyzer:
    """Dependency to get <PERSON><PERSON><PERSON> instance"""
    return request.app.state.analyzer


@router.get("/")
async def health_check():
    """
    🏥 Basic health check
    """
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "Delphi"
    }


@router.get("/detailed")
async def detailed_health_check(analyzer: DelphiAnalyzer = Depends(get_analyzer)):
    """
    🔍 Detailed health check including all systems
    """
    health_data = {
        "timestamp": datetime.utcnow().isoformat(),
        "service": "Delphi",
        "version": "0.1.0",
        "python_version": sys.version,
        "status": "healthy",
        "checks": {}
    }

    # Check database connection
    try:
        db_status = await test_db_connection()
        health_data["checks"]["database"] = {
            "status": "healthy" if db_status else "unhealthy",
            "message": "📊 Database connection active" if db_status else "❌ Database unavailable"
        }
    except Exception as e:
        health_data["checks"]["database"] = {
            "status": "error",
            "message": f"🚨 Database connection failed: {str(e)}"
        }

    # Check Analysis tools
    try:
        tools_count = len(analyzer.tools)
        health_data["checks"]["analysis_tools"] = {
            "status": "healthy" if tools_count > 0 else "warning",
            "message": f"🛠️ {tools_count} tools available",
            "tools_loaded": tools_count
        }
    except Exception as e:
        health_data["checks"]["analysis_tools"] = {
            "status": "error",
            "message": f"🚨 Analysis tools unavailable: {str(e)}"
        }

    # Check AI service
    try:
        ai_status = hasattr(analyzer, 'instrument_llm') and analyzer.instrument_llm is not None
        health_data["checks"]["ai_service"] = {
            "status": "healthy" if ai_status else "unhealthy",
            "message": "🤖 AI service connection active" if ai_status else "❌ AI service disconnected",
            "model": getattr(analyzer.instrument_llm, 'model_name', 'Unknown') if ai_status else None
        }
    except Exception as e:
        health_data["checks"]["ai_service"] = {
            "status": "error",
            "message": f"🚨 AI service error: {str(e)}"
        }

    # Check system resources
    try:
        import psutil
        health_data["checks"]["system"] = {
            "status": "healthy",
            "message": "💻 System resources normal",
            "cpu_percent": psutil.cpu_percent(),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_percent": psutil.disk_usage('/').percent
        }
    except ImportError:
        health_data["checks"]["system"] = {
            "status": "info",
            "message": "📊 System monitoring not available (psutil not installed)"
        }
    except Exception as e:
        health_data["checks"]["system"] = {
            "status": "warning",
            "message": f"⚠️ System check failed: {str(e)}"
        }

    # Determine overall status
    failed_checks = [check for check in health_data["checks"].values() if check["status"] in ["error", "unhealthy"]]
    if failed_checks:
        health_data["status"] = "unhealthy"
        health_data["temple_status"] = "🚨 Oracle temple has issues"
    else:
        health_data["temple_status"] = "🏛️ Oracle temple is fully operational"

    return health_data


@router.get("/analyzer")
async def analyzer_status(analyzer: DelphiAnalyzer = Depends(get_analyzer)):
    """
    📊 Check Analyzer-specific status and capabilities
    """
    try:
        tools_list = analyzer.list_analysis_tools()

        return {
            "analyzer_status": "ready_for_requests",
            "message": "📊 The analyzer is ready to process data",
            "analysis_tools": {
                "total_count": len(tools_list),
                "available_tools": [tool["name"] for tool in tools_list],
                "tool_capabilities": tools_list
            },
            "ai_model": getattr(analyzer.instrument_llm, 'model_name', 'Unknown'),
            "system_condition": "🖥️ System is operational and ready"
        }
    except Exception as e:
        return {
            "analyzer_status": "processing_impaired",
            "message": f"⚠️ Analyzer experiencing processing issues: {str(e)}",
            "system_condition": "⚠️ System experiencing technical difficulties"
        }


@router.get("/readiness")
async def readiness_check(analyzer: DelphiAnalyzer = Depends(get_analyzer)):
    """
    ✅ Kubernetes readiness probe
    """
    try:
        # Quick checks for essential services
        db_ready = await test_db_connection()
        analyzer_ready = len(analyzer.tools) > 0
        ai_ready = hasattr(analyzer, 'instrument_llm') and analyzer.instrument_llm is not None

        if db_ready and analyzer_ready and ai_ready:
            return {
                "ready": True,
                "message": "🖥️ System ready to process requests"
            }
        else:
            return {
                "ready": False,
                "message": "⏳ System still initializing services",
                "details": {
                    "database": db_ready,
                    "analysis_tools": analyzer_ready,
                    "ai_service": ai_ready
                }
            }
    except Exception as e:
        return {
            "ready": False,
            "message": f"🚨 System not ready: {str(e)}"
        }


@router.get("/liveness")
async def liveness_check():
    """
    💓 Kubernetes liveness probe
    """
    try:
        # Very basic check - just ensure the service is responding
        return {
            "alive": True,
            "message": "💓 System heartbeat strong",
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        return {
            "alive": False,
            "message": f"💀 System heartbeat weak: {str(e)}"
        }
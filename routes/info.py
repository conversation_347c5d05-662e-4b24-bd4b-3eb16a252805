from fastapi import APIRouter, Depends, Request
from services.oracle import DelphiAnalyzer

router = APIRouter(prefix="/info", tags=["System Info"])


def get_analyzer(request: Request) -> DelphiAnalyzer:
    """Dependency to get Analyzer instance"""
    return request.app.state.analyzer


# System information endpoint
@router.get("/", tags=["System Info"])
async def system_info(analyzer: DelphiAnalyzer = Depends(get_analyzer)):
    """
    📊 Delphi System Information

    Get system status, capabilities, and usage guide.
    """
    return {
        "success": True,
        "data": {
            "system_name": "Delphi Data Analyzer",
            "status": "operational",
            "analysis_tools_available": len(analyzer.tools),
            "capabilities": [
                "📊 Route Analysis",
                "💰 Financial Insights",
                "📋 Contract Monitoring",
                "🚨 Alert Systems"
            ],
            "usage_guide": {
                "quick_analysis": "GET /api/v1/analysis/quick?query=your_question",
                "full_analysis": "POST /api/v1/analysis with your query",
                "check_status": "GET /api/v1/analysis/{task_id}",
                "list_tools": "GET /api/v1/analysis/tools",
                "system_health": "GET /api/v1/health"
            }
        },
        "message": "📊 Delphi system information retrieved successfully",
        "timestamp": "2025-07-04T14:19:14.013111"
    }




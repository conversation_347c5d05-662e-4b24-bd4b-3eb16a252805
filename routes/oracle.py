from fastapi import APIRouter, Depends, Request
from services.oracle import DelphiAnalyzer

router = APIRouter(prefix="/analyzer", tags=["Analyzer"])


def get_analyzer(request: Request) -> DelphiAnalyzer:
    """Dependency to get Analyzer instance"""
    return request.app.state.analyzer


# Root endpoint
@router.get("/", tags=["Analyzer"])
async def analyzer_info(analyzer: DelphiAnalyzer = Depends(get_analyzer)):
    """
    📊 Welcome to the Delphi Data Analyzer

    The analyzer is ready to process your logistics data requests.
    """
    return {
        "message": "📊 Welcome to Delphi Data Analyzer!",
        "analyzer_status": "ready_for_requests",
        "analysis_tools_available": len(analyzer.tools),
        "capabilities": [
            "📊 Route Analysis",
            "💰 Financial Insights",
            "📋 Contract Monitoring",
            "🚨 Alert Systems"
        ],
        "usage_guide": {
            "submit_request": "POST /api/v1/analyze with your query",
            "check_status": "GET /api/v1/analyze/status/{task_id}",
            "list_tools": "GET /api/v1/tools",
            "system_health": "GET /api/v1/health"
        }
    }


# Direct analysis endpoint (for simple queries)
@router.get("/analyze", tags=["Analyzer"])
async def quick_analysis(query: str, analyzer: DelphiAnalyzer = Depends(get_analyzer)):
    """
    📊 Quick data analysis

    For simple requests that don't require background processing.
    """
    if not query:
        return {"error": "The analyzer requires a query to process data"}

    try:
        # Simple, direct analysis
        result = await analyzer.analyze_data(query)
        return {
            "query": query,
            "analysis_response": result,
            "request_type": "immediate_analysis"
        }
    except Exception as e:
        return {
            "error": f"The analyzer encountered processing issues: {str(e)}",
            "guidance": "Try a more specific query or use the full analysis endpoint"
        }

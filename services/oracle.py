import json
from typing import Dict, List, Any
from openai import AsyncOpenAI
import google.generativeai as genai
from google.generativeai.types import Tool as GeminiTool
from tools import get_tools, BaseTool


class DelphiAnalyzer:
    """Delphi Data Analyzer - Provides insights through AI and data tools"""

    def __init__(self, ai_settings: Dict[str, Any]):

        if ai_settings["llm"] == "openai":
            self.instrument_llm = InstrumentOpenAI(ai_settings["openai_api_key"], ai_settings["openai_model"])
        elif ai_settings["llm"] == "gemini":
            self.instrument_llm = InstrumentGemini(ai_settings["gemini_api_key"], ai_settings["gemini_model"])
        else:
            raise ValueError("LLM not supported.")

        self.tools = get_tools()
        print(f"Loaded {len(self.tools)} tools.")

    async def analyze_data(self, query: str, context: Dict = None) -> Dict[str, Any]:
        """
        Main analysis method - processes query and returns insights
        """
        print(f"📊 Analyzer processing request for: {query}")

        # Prepare tools for the chosen LLM
        self.instrument_llm.prepare_tools(self.tools)

        # Initial analysis - determine needed tools

        return await self.instrument_llm.consult(query, context)

    def list_analysis_tools(self) -> List[Dict[str, str]]:
        """List all available analysis tools"""
        return [
            {
                "name": tool.schema.name,
                "description": tool.schema.description,
                "tool_status": "active"
            }
            for tool in self.tools.values()
        ]


class InstrumentGemini:

    def __init__(self, api_key: str, model: str):
        genai.configure(api_key=api_key)
        self.client = genai.GenerativeModel(model)
        self.model_name = model
        print(f"Delphi initialized with Gemini model: {self.model_name}")

    def prepare_tools(self, tools: Dict[str, BaseTool]):
        self.tools = tools

        self.llm_tools = [
            GeminiTool.from_dict({
                "function_declarations": [{
                    "name": tool.schema.name,
                    "description": tool.schema.description,
                    "parameters": tool.schema.parameters
                }]
            })
            for tool in tools.values()
        ]

    async def consult(self, query: str, context: Dict = None, llm_tools: List[GeminiTool] = None) -> Dict[str, Any]:

        tool_results = {}
        tools_used = []

        # Initial analysis - determine needed tools
        response = await self.client.generate_content(
            contents=[
                {
                    "role": "user",
                    "parts": [
                        {"text": f"""You are a professional logistics data analyzer.
                                 Use available analysis tools to gather data, then provide actionable insights.
                                 Analysis Request: {query}"""}
                    ]
                }
            ],
            tools=llm_tools,
            tool_config={"function_calling_config": {"mode": "AUTO"}},
            generation_config={"temperature": 0.1}
        )

        if response.candidates and response.candidates[0].content.parts:
            for part in response.candidates[0].content.parts:
                if part.function_call:
                    tool_name = part.function_call.name
                    tool_args = {k: v for k, v in part.function_call.args.items()}

                    if tool_name in self.tools:
                        print(f"🔧 Executing tool: {tool_name}")
                        tool_results[tool_name] = await self.tools[tool_name].execute(**tool_args)
                        tools_used.append(tool_name)

        # Final analysis and report generation
        analysis_response = await self.client.generate_content(
            contents=[
                {
                    "role": "user",
                    "parts": [
                        {"text": f"""You are a professional logistics data analyzer. Provide clear, actionable insights.
                                 Present findings in a structured, professional manner.
                                 Analysis Request: {query}
                                 Context: {json.dumps(context or {}, indent=2)}
                                 Processed Data: {json.dumps(tool_results, indent=2, default=str)}

                                 Provide your analysis results:
                                 """}
                    ]
                }
            ],
            generation_config={"temperature": 0.7, "max_output_tokens": 2000}
        )

        return {
            "analysis_result": analysis_response.text,
            "processed_data": tool_results,
            "tools_invoked": tools_used,
            "analysis_status": "complete"
        }


class InstrumentOpenAI:

    def __init__(self, api_key: str, model: str):
        self.client = AsyncOpenAI(api_key=api_key)
        self.model_name = model
        print(f"Delphi initialized with OpenAI model: {self.model_name}")

    def prepare_tools(self, tools: Dict[str, BaseTool]):
        self.tools = tools

        self.llm_tools = [
            {
                "type": "function",
                "function": {
                    "name": tool.schema.name,
                    "description": tool.schema.description,
                    "parameters": tool.schema.parameters
                }
            }
            for tool in tools.values()
        ]

    async def consult(self, query: str, context: Dict = None, llm_tools: List[Dict[str, Any]] = None) -> Dict[str, Any]:

        tool_results = {}
        tools_used = []

        # Initial analysis - determine needed tools
        response = await self.client.chat.completions.create(
            model=self.model_name,
            messages=[
                {
                    "role": "system",
                    "content": """You are a professional logistics data analyzer.
                     Use available analysis tools to gather data, then provide actionable insights."""
                },
                {
                    "role": "user",
                    "content": query
                }
            ],
            tools=llm_tools,
            tool_choice="auto",
            temperature=0.1
        )

        if response.choices[0].message.tool_calls:
            for tool_call in response.choices[0].message.tool_calls:
                tool_name = tool_call.function.name
                tool_args = json.loads(tool_call.function.arguments)

                if tool_name in self.tools:
                    print(f"🔧 Executing tool: {tool_name}")
                    tool_results[tool_name] = await self.tools[tool_name].execute(**tool_args)
                    tools_used.append(tool_name)

        # Final analysis and report generation
        analysis_response = await self.client.chat.completions.create(
            model=self.model_name,
            messages=[
                {
                    "role": "system",
                    "content": """You are a professional logistics data analyzer. Provide clear, actionable insights.
                     Present findings in a structured, professional manner."""
                },
                {
                    "role": "user",
                    "content": f"""
                     Analysis Request: {query}
                     Context: {json.dumps(context or {}, indent=2)}
                     Processed Data: {json.dumps(tool_results, indent=2, default=str)}

                     Provide your analysis results:
                     """
                }
            ],
            temperature=0.7,
            max_tokens=2000
        )

        return {
            "analysis_result": analysis_response.choices[0].message.content,
            "processed_data": tool_results,
            "tools_invoked": tools_used,
            "analysis_status": "complete"
        }

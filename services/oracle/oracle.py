import json
from typing import Dict, List, Any
from openai import AsyncOpenAI
import google.generativeai as genai
from google.generativeai.types import Tool as GeminiTool
from tools import get_tools


class DelphiOracle:
    """The Oracle of Delphi - Provides insights through AI and data tools"""

    def __init__(self, openai_api_key: str | None = None, openai_model: str = "gpt-4o-mini", gemini_api_key: str | None = None, gemini_model: str = "gemini-1.5-flash"):
        self.openai_client = None
        self.gemini_model = None
        self.model_name = None

        if openai_api_key:
            self.openai_client = AsyncOpenAI(api_key=openai_api_key)
            self.model_name = openai_model
            print(f"🏛️  Delphi Oracle initialized with OpenAI model: {self.model_name}")
        elif gemini_api_key:
            genai.configure(api_key=gemini_api_key)
            self.gemini_model = genai.GenerativeModel(gemini_model)
            self.model_name = gemini_model
            print(f"🏛️  Delphi Oracle initialized with Google Gemini model: {self.model_name}")
        else:
            raise ValueError("Either OpenAI or Gemini API key must be provided.")

        self.tools = get_tools()
        print(f"Loaded {len(self.tools)} tools.")

    async def divine_insights(self, query: str, context: Dict = None) -> Dict[str, Any]:
        """
        The Oracle's main method - divine insights from the query
        """
        print(f"🔮 Oracle consulting the data spirits for: {query}")

        # Prepare tools for the chosen LLM
        llm_tools = []
        if self.openai_client:
            llm_tools = [
                {
                    "type": "function",
                    "function": {
                        "name": tool.schema.name,
                        "description": tool.schema.description,
                        "parameters": tool.schema.parameters
                    }
                }
                for tool in self.tools.values()
            ]
        elif self.gemini_model:
            llm_tools = [
                GeminiTool.from_dict({
                    "function_declarations": [{
                        "name": tool.schema.name,
                        "description": tool.schema.description,
                        "parameters": tool.schema.parameters
                    }]
                })
                for tool in self.tools.values()
            ]

        # Oracle's first consultation - determine needed tools
        tool_results = {}
        tools_used = []

        if self.openai_client:
            response = await self.openai_client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {
                        "role": "system",
                        "content": """You are the Oracle of Delphi, a wise logistics analyst. 
                        Use your divine tools to gather data, then provide prophetic insights."""
                    },
                    {
                        "role": "user",
                        "content": query
                    }
                ],
                tools=llm_tools,
                tool_choice="auto",
                temperature=0.1
            )

            if response.choices[0].message.tool_calls:
                for tool_call in response.choices[0].message.tool_calls:
                    tool_name = tool_call.function.name
                    tool_args = json.loads(tool_call.function.arguments)

                    if tool_name in self.tools:
                        print(f"🔧 Invoking tool: {tool_name}")
                        tool_results[tool_name] = await self.tools[tool_name].execute(**tool_args)
                        tools_used.append(tool_name)

            # Oracle's final prophecy
            prophecy_response = await self.openai_client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {
                        "role": "system",
                        "content": """You are the Oracle of Delphi. Provide clear, actionable logistics insights.
                        Speak with wisdom and authority, but keep it professional and helpful."""
                    },
                    {
                        "role": "user",
                        "content": f"""
                        Divine Query: {query}
                        Context: {json.dumps(context or {}, indent=2)}
                        Sacred Data: {json.dumps(tool_results, indent=2, default=str)}

                        Provide your prophetic insights:
                        """
                    }
                ],
                temperature=0.7,
                max_tokens=2000
            )
            prophecy_content = prophecy_response.choices[0].message.content

        elif self.gemini_model:
            # Gemini's first consultation - determine needed tools
            response = await self.gemini_model.generate_content(
                contents=[
                    {
                        "role": "user",
                        "parts": [
                            {"text": f"""You are the Oracle of Delphi, a wise logistics analyst. 
                            Use your divine tools to gather data, then provide prophetic insights.
                            Divine Query: {query}"""}
                        ]
                    }
                ],
                tools=llm_tools,
                tool_config={"function_calling_config": {"mode": "AUTO"}},
                generation_config={"temperature": 0.1}
            )

            if response.candidates and response.candidates[0].content.parts:
                for part in response.candidates[0].content.parts:
                    if part.function_call:
                        tool_name = part.function_call.name
                        tool_args = {k: v for k, v in part.function_call.args.items()}

                        if tool_name in self.tools:
                            print(f"🔧 Invoking tool: {tool_name}")
                            tool_results[tool_name] = await self.tools[tool_name].execute(**tool_args)
                            tools_used.append(tool_name)

            # Oracle's final prophecy for Gemini
            prophecy_response = await self.gemini_model.generate_content(
                contents=[
                    {
                        "role": "user",
                        "parts": [
                            {"text": f"""You are the Oracle of Delphi. Provide clear, actionable logistics insights.
                            Speak with wisdom and authority, but keep it professional and helpful.
                            Divine Query: {query}
                            Context: {json.dumps(context or {}, indent=2)}
                            Sacred Data: {json.dumps(tool_results, indent=2, default=str)}

                            Provide your prophetic insights:
                            """}
                        ]
                    }
                ],
                generation_config={"temperature": 0.7, "max_output_tokens": 2000}
            )
            prophecy_content = prophecy_response.text

        else:
            raise ValueError("No LLM client initialized.")

        return {
            "prophecy": prophecy_content,
            "sacred_data": tool_results,
            "tools_invoked": tools_used,
            "oracle_status": "divination_complete"
        }

    def list_divine_tools(self) -> List[Dict[str, str]]:
        """List all available divine tools"""
        return [
            {
                "name": tool.schema.name,
                "description": tool.schema.description,
                "divine_power": "active"
            }
            for tool in self.tools.values()
        ]
from tools.base import BaseTool, ToolSchema
from typing import Dict, Any
import asyncio
import aiomysql
from database.connection import get_db_connection


class OverdueClientsTool(BaseTool):
    """Tool to identify clients with the largest overdue amounts."""

    def __init__(self):
        self.schema = ToolSchema(
            name="get_top_overdue_clients",
            description="Identifies clients with the largest overdue payment amounts",
            parameters={
                "type": "object",
                "properties": {
                    "limit": {
                        "type": "integer",
                        "description": "Maximum number of clients to return (default: 5)",
                        "default": 5,
                        "minimum": 1,
                        "maximum": 100
                    }
                },
                "required": []
            }
        )

    async def execute(self, limit: int = 5) -> Dict[str, Any]:
        """
        Executes the search for clients with the largest overdue amounts.

        Args:
            limit: Maximum number of clients to return

        Returns:
            Dictionary with overdue clients and statistics
        """
        try:
            conn = await get_db_connection()
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                # Main query for overdue clients
                main_query = """
                SELECT
                    c.id_cliente,
                    c.razao as client_name,
                    c.cnpj_cpf,
                    SUM(fl.valor) as overdue_amount,
                    COUNT(fl.id_financeiro_lancamento) as overdue_invoices,
                    MIN(fl.data_vencimento) as oldest_overdue_date,
                    MAX(fl.data_vencimento) as newest_overdue_date,
                    AVG(DATEDIFF(CURDATE(), fl.data_vencimento)) as avg_days_overdue
                FROM financeiro_lancamento fl
                JOIN cliente c ON fl.id_cliente = c.id_cliente
                WHERE fl.tipo_lancamento = 'R'
                  AND fl.status = 'A'
                  AND fl.data_vencimento < CURDATE()
                GROUP BY fl.id_cliente, c.razao, c.cnpj_cpf
                ORDER BY overdue_amount DESC
                LIMIT %s
                """

                await cursor.execute(main_query, (limit,))
                results = await cursor.fetchall()

                # Process results
                overdue_clients = []
                for client in results:
                    # Convert dates to ISO string
                    if client.get('oldest_overdue_date'):
                        if isinstance(client['oldest_overdue_date'], date):
                            client['oldest_overdue_date'] = client['oldest_overdue_date'].isoformat()

                    if client.get('newest_overdue_date'):
                        if isinstance(client['newest_overdue_date'], date):
                            client['newest_overdue_date'] = client['newest_overdue_date'].isoformat()

                    # Convert Decimal to float
                    if client.get('overdue_amount') and hasattr(client['overdue_amount'], '__float__'):
                        client['overdue_amount'] = float(client['overdue_amount'])

                    if client.get('avg_days_overdue') and hasattr(client['avg_days_overdue'], '__float__'):
                        client['avg_days_overdue'] = round(float(client['avg_days_overdue']), 1)

                    # Convert bytes to string if necessary
                    for key, value in client.items():
                        if isinstance(value, bytes):
                            client[key] = value.decode('utf-8')

                    overdue_clients.append(client)

                # General statistics
                stats_query = """
                SELECT
                    COUNT(DISTINCT fl.id_cliente) as total_overdue_clients,
                    SUM(fl.valor) as total_overdue_amount,
                    COUNT(fl.id_financeiro_lancamento) as total_overdue_invoices,
                    AVG(DATEDIFF(CURDATE(), fl.data_vencimento)) as avg_days_overdue_all
                FROM financeiro_lancamento fl
                WHERE fl.tipo_lancamento = 'R'
                  AND fl.status = 'A'
                  AND fl.data_vencimento < CURDATE()
                """

                await cursor.execute(stats_query)
                stats = await cursor.fetchone()

                # Convert Decimal to float in statistics
                if stats:
                    if stats.get('total_overdue_amount') and hasattr(stats['total_overdue_amount'], '__float__'):
                        stats['total_overdue_amount'] = float(stats['total_overdue_amount'])

                    if stats.get('avg_days_overdue_all') and hasattr(stats['avg_days_overdue_all'], '__float__'):
                        stats['avg_days_overdue_all'] = round(float(stats['avg_days_overdue_all']), 1)

                conn.close()

                # Calculate percentage of top clients
                top_percentage = 0
                if stats and stats['total_overdue_amount'] > 0:
                    top_amount = sum(client['overdue_amount'] for client in overdue_clients)
                    top_percentage = round((top_amount / stats['total_overdue_amount']) * 100, 1)

                # Determine risk level
                risk_level = "low"
                if stats and stats['total_overdue_amount']:
                    if stats['total_overdue_amount'] > 100000:  # R$ 100k
                        risk_level = "critical"
                    elif stats['total_overdue_amount'] > 50000:  # R$ 50k
                        risk_level = "high"
                    elif stats['total_overdue_amount'] > 10000:  # R$ 10k
                        risk_level = "medium"

                return {
                    "success": True,
                    "data": {
                        "top_overdue_clients": overdue_clients,
                        "statistics": stats or {
                            "total_overdue_clients": 0,
                            "total_overdue_amount": 0.0,
                            "total_overdue_invoices": 0,
                            "avg_days_overdue_all": 0.0
                        },
                        "analysis": {
                            "top_clients_percentage": top_percentage,
                            "risk_level": risk_level,
                            "returned_count": len(overdue_clients),
                            "limit_applied": limit
                        }
                    },
                    "message": f"Found {len(overdue_clients)} overdue clients (top {limit})",
                    "insight": f"Risk {risk_level}: R$ {stats['total_overdue_amount']:.2f} overdue from {stats['total_overdue_clients']} clients. Top {limit} represent {top_percentage}% of the total" if stats else "No overdue clients found"
                }

        except Exception as e:
            return {
                "success": False,
                "error": f"Error fetching overdue clients: {str(e)}",
                "data": {
                    "top_overdue_clients": [],
                    "statistics": {
                        "total_overdue_clients": 0,
                        "total_overdue_amount": 0.0,
                        "total_overdue_invoices": 0,
                        "avg_days_overdue_all": 0.0
                    },
                    "analysis": {
                        "top_clients_percentage": 0,
                        "risk_level": "unknown",
                        "returned_count": 0
                    }
                },
                "guidance": "Verify the database connection and the financeiro_lancamento table structure"
            }
